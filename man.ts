// server.ts

/**
 * Deno 脚本：创建一个 OpenAI 兼容的 API 服务器，
 * 通过 Atlassian AI Gateway 代理请求到 Anthropic、Google 等模型。
 *
 * ⚡️ 版本变更
 *   • 使用 [Oak](https://deno.land/x/oak) 代替 `std/http/serve`。
 *   • 移除大部分外部依赖，仅保留 Oak 与 `std/streams`（用于文本行分割）。
 *   • 凭据采用硬编码 `CREDENTIALS` 数组，并在 401/403/5xx 时轮询下一个。
 *   • 指数退避：初始 500 ms，每次 ×2，上限 16 s。
 *
 * 运行：
 *   deno run --allow-net --allow-env server.ts
 *
 * 端点：
 *   GET  /v1/models
 *   POST /v1/chat/completions   (支持 stream: true | false)
 */

// ------------------------ Imports ------------------------
import { Application, Router, Status } from "https://deno.land/x/oak@v12.6.1/mod.ts";
import { TextLineStream } from "https://deno.land/std@0.224.0/streams/text_line_stream.ts";

// ---------------------- Configuration --------------------
const DEBUG_MODE = Deno.env.get("DEBUG_MODE") === "true";

// 上游 Atlassian AI Gateway
const ROVO_DEV_PROXY_URL = "https://api.atlassian.com/rovodev/v2/proxy/ai";
const UNIFIED_CHAT_PATH = "/v2/beta/chat";
const ATLASSIAN_API_ENDPOINT = `${ROVO_DEV_PROXY_URL}${UNIFIED_CHAT_PATH}`;

// 支持的模型列表
const SUPPORTED_MODELS = [
  "anthropic:claude-3-5-sonnet-v2@20241022",
  "anthropic:claude-3-7-sonnet@20250219",
  "anthropic:claude-sonnet-4@20250514",
  "anthropic:claude-opus-4@20250514",
  "google:gemini-2.0-flash-001",
  "google:gemini-2.5-pro-preview-03-25",
  "google:gemini-2.5-flash-preview-04-17",
  "bedrock:anthropic.claude-3-5-sonnet-20241022-v2:0",
  "bedrock:anthropic.claude-3-7-sonnet-20250219-v1:0",
  "bedrock:anthropic.claude-sonnet-4-20250514-v1:0",
  "bedrock:anthropic.claude-opus-4-20250514-v1:0",
];

// ⚠️ 硬编码凭据池：email / api token
const CREDENTIALS: Array<[string, string]> = [
  ["<EMAIL>", "************************************************************************************************************************************************************************************************"],
  // 继续添加更多 (如有需要)
];

// --------------------- Helper Utilities ------------------

/** 构造 Atlassian 基础认证头 */
function buildAuthHeaders(email: string, apiToken: string): Headers {
  const encoded = btoa(`${email}:${apiToken}`);
  return new Headers({
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": `Basic ${encoded}`,
    "X-Atlassian-EncodedToken": encoded,
  });
}

/**
 * 从模型 ID 中移除前缀 (`anthropic:` / `google:` 等)。
 */
function transformModelId(modelId: string): string {
  return modelId.split(":" ).pop() ?? modelId;
}

/**
 * 将 Atlassian 非流式响应转换为 OpenAI 兼容格式。
 */
function toOpenAI(atlas: any, modelId: string) {
  const usage = atlas.platform_attributes?.metrics?.usage || {};
  return {
    id: atlas.response_payload.id,
    object: "chat.completion",
    created: atlas.response_payload.created,
    model: modelId,
    choices: atlas.response_payload.choices.map((c: any) => ({
      index: c.index,
      message: c.message,
      finish_reason: c.finish_reason,
    })),
    usage: {
      prompt_tokens: usage.prompt_tokens,
      completion_tokens: usage.completion_tokens,
      total_tokens: usage.total_tokens,
    },
  };
}

// ----------------------- Streaming -----------------------
class StreamProcessor extends TransformStream<string, Uint8Array> {
  private isFirst = true;
  constructor(private readonly requestedModelId: string) {
    super({
      transform: (chunk, ctl) => this.handle(chunk, ctl),
      flush: (ctl) => this.done(ctl),
    });
  }

  handle(chunk: string, ctl: TransformStreamDefaultController<Uint8Array>) {
    if (!chunk || !chunk.trim().startsWith("data:")) return;
    const payload = chunk.trim().slice(5).trim();

    if (payload === "[DONE]") return; // 上游 DONE 信号忽略

    let parsed;
    try {
      parsed = JSON.parse(payload);
    } catch {
      DEBUG_MODE && console.error("无法解析 JSON:", payload);
      return;
    }

    const choice = parsed.response_payload?.choices?.[0];
    if (!choice) return;

    const delta: Record<string, unknown> = {};
    if (this.isFirst && choice.message?.role) {
      delta.role = choice.message.role;
      this.isFirst = false;
    }

    const content = choice.message?.content?.[0]?.text;
    if (content) delta.content = content;

    if (Object.keys(delta).length === 0 && !choice.finish_reason) return;

    const openChunk = {
      id: parsed.response_payload.id || `chatcmpl-${Date.now()}`,
      object: "chat.completion.chunk",
      created: parsed.response_payload.created || Math.floor(Date.now() / 1000),
      model: this.requestedModelId,
      choices: [{
        index: choice.index,
        delta,
        finish_reason: choice.finish_reason ?? null,
      }],
    };

    ctl.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(openChunk)}\n\n`));
  }

  done(ctl: TransformStreamDefaultController<Uint8Array>) {
    ctl.enqueue(new TextEncoder().encode("data: [DONE]\n\n"));
  }
}

// ------------- Upstream Request w/ Retry & Backoff -------------
async function fetchWithRetry(body: unknown, credentialStart = 0, stream = false) {
  let attempt = 0;
  let delay = 500; // 0.5s
  let credIdx = credentialStart % CREDENTIALS.length;

  while (attempt < CREDENTIALS.length) {
    const [email, token] = CREDENTIALS[credIdx];
    const headers = buildAuthHeaders(email, token);
    try {
      const resp = await fetch(ATLASSIAN_API_ENDPOINT, {
        method: "POST",
        headers,
        body: JSON.stringify(body),
      });

      if (resp.ok) return resp; // ✅ 成功

      if ([401, 403].includes(resp.status) || resp.status >= 500) {
        DEBUG_MODE && console.warn(`凭据 #${credIdx} 失败 (${resp.status}), 尝试下一个...`);
        // 失败则等待退避后尝试下一个凭据
        await new Promise((r) => setTimeout(r, delay));
        delay = Math.min(delay * 2, 16_000);
        credIdx = (credIdx + 1) % CREDENTIALS.length;
        attempt++;
        continue;
      }
      // 其他错误直接抛出
      throw new Error(`Upstream error ${resp.status}`);
    } catch (err) {
      // 网络层错误 -> 退避 & 切换凭据
      DEBUG_MODE && console.error("fetch error", err);
      await new Promise((r) => setTimeout(r, delay));
      delay = Math.min(delay * 2, 16_000);
      credIdx = (credIdx + 1) % CREDENTIALS.length;
      attempt++;
    }
  }
  throw new Error("All credentials exhausted.");
}

// ----------------------- Route Handlers ------------------
function modelsHandler() {
  const now = Math.floor(Date.now() / 1000);
  return {
    object: "list",
    data: SUPPORTED_MODELS.map((id) => ({ id, object: "model", created: now, owned_by: "system" })),
  };
}

async function chatHandler(ctx: any) {
  if (ctx.request.method !== "POST") {
    ctx.response.status = Status.MethodNotAllowed;
    ctx.response.body = { error: "Method Not Allowed" };
    return;
  }

  const bodyJson = await ctx.request.body({ type: "json" }).value;
  const requestedModel = bodyJson.model;

  const upstreamBody = {
    request_payload: {
      messages: bodyJson.messages,
      temperature: bodyJson.temperature,
      stream: bodyJson.stream || false,
    },
    platform_attributes: {
      model: transformModelId(requestedModel),
    },
  };

  try {
    const upstream = await fetchWithRetry(upstreamBody, 0, bodyJson.stream);

    if (bodyJson.stream) {
      if (!upstream.body) {
        ctx.throw(Status.InternalServerError, "Upstream has no body");
      }
      const processed = upstream.body
        .pipeThrough(new TextDecoderStream())
        .pipeThrough(new TextLineStream())
        .pipeThrough(new StreamProcessor(requestedModel));

      ctx.response.type = "text/event-stream";
      ctx.response.headers.set("Cache-Control", "no-cache");
      ctx.response.headers.set("Connection", "keep-alive");
      ctx.response.body = processed;
    } else {
      const json = await upstream.json();
      ctx.response.body = toOpenAI(json, requestedModel);
    }
  } catch (err) {
    DEBUG_MODE && console.error(err);
    ctx.response.status = Status.InternalServerError;
    ctx.response.body = { error: "Upstream request failed" };
  }
}

// ------------------------ Server -------------------------
const router = new Router();
router
  .get("/v1/models", (ctx) => (ctx.response.body = modelsHandler()))
  .post("/v1/chat/completions", chatHandler);

const app = new Application();
app.use(router.routes());
app.use(router.allowedMethods());

const PORT = 8000;
console.log(`🚀 Server running on http://localhost:${PORT}`);
await app.listen({ port: PORT });